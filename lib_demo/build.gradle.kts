plugins {
    id("java-library")
    alias(libs.plugins.jetbrains.kotlin.jvm)
    application
}

java {
    sourceCompatibility = JavaVersion.VERSION_11
    targetCompatibility = JavaVersion.VERSION_11
}

kotlin {
    compilerOptions {
        jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_11)
    }
}

application {
    mainClass.set("com.zl.lib.demo.MyClassKt")
}

dependencies {
    // 协程依赖（通过bundle统一管理）
    implementation(libs.bundles.coroutines)

    // 单元测试依赖
    testImplementation(libs.bundles.testing.unit)

    // Library特有的依赖可以在这里添加
}
