plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    alias(libs.plugins.kotlin.compose)
}

android {
    namespace = "com.zl.webpagesave"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.zl.webpagesave"
        minSdk = 24
        targetSdk = 35
        versionCode = 1
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = "11"
    }
    buildFeatures {
        compose = true
    }

    // 支持 Filament 的 native 库
    packaging {
        jniLibs {
            pickFirsts += "**/libc++_shared.so"
            pickFirsts += "**/libfilament-jni.so"
        }
    }
}

dependencies {
    // 核心Android依赖
    implementation(libs.bundles.android.core)

    // 协程依赖
    implementation(libs.bundles.coroutines)

    // Compose BOM
    implementation(platform(libs.androidx.compose.bom))

    // Compose核心依赖
    implementation(libs.bundles.compose.core)

    // Compose扩展依赖
    implementation(libs.bundles.compose.extended)

    // Activity Compose
    implementation(libs.androidx.activity.compose)

    // Filament 3D渲染依赖
    implementation(libs.bundles.filament)

    // 单元测试依赖
    testImplementation(libs.bundles.testing.unit)

    // Android测试依赖
    androidTestImplementation(libs.bundles.testing.android)

    // Compose测试依赖
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.bundles.testing.compose)

    // Debug依赖
    debugImplementation(libs.bundles.debug.compose)
}