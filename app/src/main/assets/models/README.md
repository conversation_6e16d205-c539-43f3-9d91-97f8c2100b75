# 3D 模型资源

这个文件夹包含了用于 Filament Demo 的 3D 模型资源。

## 推荐的免费 3D 模型资源

### 1. Khronos Group glTF 示例模型
- **网址**: https://github.com/KhronosGroup/glTF-Sample-Models
- **许可证**: 各种开源许可证
- **推荐模型**:
  - `DamagedHelmet.gltf` - 经典的头盔模型
  - `Duck.gltf` - 简单的鸭子模型
  - `Cube.gltf` - 基础立方体
  - `Sphere.gltf` - 基础球体

### 2. Google Filament 示例资源
- **网址**: https://github.com/google/filament/tree/main/assets
- **许可证**: Apache 2.0
- **包含内容**:
  - 材质示例
  - 环境贴图
  - 基础几何体

### 3. Sketchfab 免费模型
- **网址**: https://sketchfab.com/3d-models?features=downloadable&sort_by=-likeCount&licenses=322a749bcfa841b29dff1e8a1bb74b0b
- **许可证**: CC0 (公共域)
- **搜索关键词**: "CC0", "free", "low poly"

### 4. Poly Haven (原 HDRI Haven)
- **网址**: https://polyhaven.com/models
- **许可证**: CC0 (公共域)
- **特点**: 高质量的免费 3D 模型

## 如何添加模型到项目

### 步骤 1: 下载模型
```bash
# 示例：下载 Khronos glTF 示例
git clone https://github.com/KhronosGroup/glTF-Sample-Models.git
```

### 步骤 2: 复制到 assets 文件夹
```
app/src/main/assets/models/
├── DamagedHelmet/
│   ├── DamagedHelmet.gltf
│   ├── DamagedHelmet.bin
│   └── textures/
├── Duck/
│   ├── Duck.gltf
│   └── Duck.bin
└── Cube/
    ├── Cube.gltf
    └── Cube.bin
```

### 步骤 3: 在代码中加载
```kotlin
// 在 FilamentHelper.kt 中添加模型加载代码
fun loadModel(modelPath: String) {
    val assetManager = context.assets
    val inputStream = assetManager.open(modelPath)
    val buffer = inputStream.readBytes()
    
    val assetLoader = AssetLoader(engine, materialProvider, entityManager)
    val asset = assetLoader.createAssetFromBinary(buffer)
    
    scene.addEntities(asset.entities)
}
```

## 当前实现

目前的演示使用了 Canvas 绘制的简单立方体动画，展示了：
- 3D 投影效果
- 旋转动画
- 多面体渲染
- 实时角度显示

## 升级到真实 3D 模型

要使用真实的 3D 模型，需要：

1. **完善 Filament 集成**
   ```kotlin
   // 初始化 Filament 引擎
   val engine = Engine.create()
   val renderer = engine.createRenderer()
   val scene = engine.createScene()
   ```

2. **添加模型加载器**
   ```kotlin
   // 使用 gltfio 加载 glTF 模型
   val assetLoader = AssetLoader(engine, materialProvider, entityManager)
   ```

3. **设置相机和光照**
   ```kotlin
   // 配置相机
   val camera = engine.createCamera(entityManager.create())
   camera.setProjection(45.0, aspect, 0.1, 20.0, Camera.Fov.VERTICAL)
   
   // 添加光源
   val lightEntity = entityManager.create()
   LightManager.Builder(LightManager.Type.DIRECTIONAL)
       .color(1.0f, 1.0f, 1.0f)
       .intensity(100000.0f)
       .direction(0.0f, -1.0f, 0.0f)
       .build(engine, lightEntity)
   ```

## 性能优化建议

1. **模型优化**
   - 使用低多边形模型
   - 压缩纹理
   - 合并材质

2. **渲染优化**
   - 启用视锥体剔除
   - 使用 LOD (Level of Detail)
   - 控制光源数量

3. **内存管理**
   - 及时释放不用的资源
   - 使用对象池
   - 监控内存使用

## 许可证说明

使用第三方 3D 模型时，请注意：
- 检查模型的许可证
- 遵守使用条款
- 在应用中添加适当的版权声明

## 推荐的开发工具

1. **Blender** - 免费的 3D 建模软件
2. **glTF Viewer** - 在线 glTF 模型查看器
3. **Filament Viewer** - Google 官方的 Filament 模型查看器
4. **Android Studio** - 带有 3D 模型预览功能
