package com.zl.webpagesave

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import com.zl.webpagesave.ui.theme.WebPageSaveTheme

class MainActivity : ComponentActivity() {

    companion object {
        init {
            // 初始化 Filament
            try {
                com.google.android.filament.utils.Utils.init()
            } catch (e: Exception) {
                // 如果 Utils 不可用，直接初始化 Filament
                com.google.android.filament.Filament.init()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            WebPageSaveTheme {
                MainScreen()
            }
        }
    }
}