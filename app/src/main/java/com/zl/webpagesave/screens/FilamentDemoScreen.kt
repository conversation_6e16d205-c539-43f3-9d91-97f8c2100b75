package com.zl.webpagesave.screens

import android.content.Context
import android.view.SurfaceView
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.zl.webpagesave.components.TouchableFilamentView
import com.zl.webpagesave.utils.Simple3DModels.drawRotatingCube
import com.zl.webpagesave.utils.Simple3DModels.drawRotatingOctahedron
import com.zl.webpagesave.utils.Simple3DModels.drawRotatingPyramid
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FilamentDemoScreen(
    onNavigateBack: () -> Unit
) {
    val context = LocalContext.current
    var isRendering by remember { mutableStateOf(false) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Filament 3D Demo") },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(Icons.Default.ArrowBack, contentDescription = "返回")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
        ) {
            // Filament 3D 渲染视图
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp)
                    .padding(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    if (isRendering) {
                        // 显示 3D 模型预览图
                        Simple3DModelPreview()
                    } else {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "🎮",
                                style = MaterialTheme.typography.displayLarge
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = "Filament 3D 渲染区域",
                                style = MaterialTheme.typography.titleMedium
                            )
                            Text(
                                text = "点击下方按钮开始渲染",
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    }
                }
            }
            
            // 控制面板
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Filament 3D 渲染引擎演示",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "这是一个使用 Google Filament 渲染引擎的简单演示。" +
                                "Filament 是一个基于物理的实时渲染引擎，专为移动设备优化。",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        Button(
                            onClick = {
                                isRendering = !isRendering
                            }
                        ) {
                            Text(if (isRendering) "停止渲染" else "开始渲染")
                        }

                        Button(
                            onClick = {
                                // 模拟重置操作
                                isRendering = false
                            }
                        ) {
                            Text("重置场景")
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Button(
                        onClick = {
                            // 模拟相机重置
                        },
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Text("重置相机")
                    }
                }
            }
            
            // 功能说明
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "功能特性",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    val features = listOf(
                        "基于物理的渲染 (PBR)",
                        "实时阴影和光照",
                        "材质系统和纹理",
                        "后处理效果",
                        "移动设备优化",
                        "跨平台支持 (Android/iOS/Web)",
                        "glTF 2.0 模型支持",
                        "HDR 环境贴图"
                    )
                    
                    features.forEach { feature ->
                        Row(
                            modifier = Modifier.padding(vertical = 2.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "• ",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.primary
                            )
                            Text(
                                text = feature,
                                style = MaterialTheme.typography.bodyMedium
                            )
                        }
                    }
                }
            }

            // 使用说明
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "集成说明",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "要在您的项目中使用 Filament，请按以下步骤操作：",
                        style = MaterialTheme.typography.bodyMedium
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    val steps = listOf(
                        "1. 添加 Filament 依赖到 build.gradle",
                        "2. 创建 Engine 和 Renderer 实例",
                        "3. 设置 Scene、View 和 Camera",
                        "4. 加载 3D 模型或创建几何体",
                        "5. 配置材质和光照",
                        "6. 实现渲染循环"
                    )

                    steps.forEach { step ->
                        Text(
                            text = step,
                            style = MaterialTheme.typography.bodySmall,
                            modifier = Modifier.padding(vertical = 2.dp)
                        )
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "💡 提示：这是一个演示界面，实际的 Filament 集成需要更多的原生代码和资源文件。",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

@Composable
fun Simple3DModelPreview() {
    var rotationAngle by remember { mutableStateOf(0f) }
    var currentModel by remember { mutableStateOf(0) }

    // 模型列表
    val models = listOf("立方体", "金字塔", "八面体")

    // 简单的动画效果
    LaunchedEffect(Unit) {
        while (true) {
            delay(50)
            rotationAngle += 2f
            if (rotationAngle >= 360f) rotationAngle = 0f
        }
    }

    // 自动切换模型
    LaunchedEffect(Unit) {
        while (true) {
            delay(3000) // 每3秒切换一次模型
            currentModel = (currentModel + 1) % models.size
        }
    }

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        // 使用 Canvas 绘制 3D 模型
        Canvas(
            modifier = Modifier.size(250.dp)
        ) {
            val centerX = size.width / 2
            val centerY = size.height / 2
            val modelSize = 80.dp.toPx()

            // 根据当前模型绘制不同的几何体
            when (currentModel) {
                0 -> drawRotatingCube(
                    centerX = centerX,
                    centerY = centerY,
                    size = modelSize,
                    rotationX = rotationAngle * 0.5f,
                    rotationY = rotationAngle,
                    rotationZ = rotationAngle * 0.3f
                )
                1 -> drawRotatingPyramid(
                    centerX = centerX,
                    centerY = centerY,
                    size = modelSize,
                    rotation = rotationAngle
                )
                2 -> drawRotatingOctahedron(
                    centerX = centerX,
                    centerY = centerY,
                    size = modelSize,
                    rotation = rotationAngle
                )
            }
        }

        // 模型信息显示
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.align(Alignment.BottomCenter).padding(16.dp)
        ) {
            Text(
                text = "🎮 ${models[currentModel]} 演示",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.primary
            )
            Text(
                text = "旋转角度: ${rotationAngle.toInt()}°",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = "自动切换模型中...",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // 模型切换指示器
        Row(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            models.forEachIndexed { index, _ ->
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(
                            color = if (index == currentModel)
                                MaterialTheme.colorScheme.primary
                            else
                                MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.3f),
                            shape = androidx.compose.foundation.shape.CircleShape
                        )
                )
            }
        }
    }
}


