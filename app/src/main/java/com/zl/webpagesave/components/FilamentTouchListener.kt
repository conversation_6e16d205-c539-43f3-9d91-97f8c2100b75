package com.zl.webpagesave.components

import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import kotlin.math.*

/**
 * Filament 3D 视图的触摸监听器
 * 
 * 处理旋转、缩放等触摸交互
 */
class FilamentTouchListener(
    private val renderer: FilamentRenderer,
    private val onTransformChanged: (rotationX: Float, rotationY: Float, scale: Float) -> Unit
) : View.OnTouchListener {
    
    // 手势检测器
    private val gestureDetector: GestureDetector
    private val scaleGestureDetector: ScaleGestureDetector
    
    // 触摸状态
    private var lastTouchX = 0f
    private var lastTouchY = 0f
    private var isRotating = false
    
    // 旋转和缩放状态
    private var currentRotationX = 0f
    private var currentRotationY = 0f
    private var currentScale = 1f
    
    // 触摸灵敏度配置
    private val rotationSensitivity = 0.01f
    private val minScale = 0.5f
    private val maxScale = 3.0f
    
    init {
        // 初始化手势检测器
        gestureDetector = GestureDetector(renderer.context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onDown(e: MotionEvent): Boolean {
                return true
            }
            
            override fun onSingleTapUp(e: MotionEvent): Boolean {
                // 可以在这里处理点击事件，比如选择模型中的对象
                return true
            }
            
            override fun onDoubleTap(e: MotionEvent): Boolean {
                // 双击重置视角
                resetView()
                return true
            }
        })
        
        // 初始化缩放手势检测器
        scaleGestureDetector = ScaleGestureDetector(renderer.context, object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
            override fun onScale(detector: ScaleGestureDetector): Boolean {
                val scaleFactor = detector.scaleFactor
                currentScale = (currentScale * scaleFactor).coerceIn(minScale, maxScale)
                
                // 更新渲染器
                renderer.scale = currentScale
                onTransformChanged(currentRotationX, currentRotationY, currentScale)
                
                return true
            }
        })
    }
    
    override fun onTouch(view: View, event: MotionEvent): Boolean {
        // 处理缩放手势
        scaleGestureDetector.onTouchEvent(event)
        
        // 处理其他手势
        gestureDetector.onTouchEvent(event)
        
        // 处理旋转手势
        when (event.action and MotionEvent.ACTION_MASK) {
            MotionEvent.ACTION_DOWN -> {
                if (!scaleGestureDetector.isInProgress) {
                    lastTouchX = event.x
                    lastTouchY = event.y
                    isRotating = true
                }
            }
            
            MotionEvent.ACTION_POINTER_DOWN -> {
                // 多点触摸开始，停止旋转
                isRotating = false
            }
            
            MotionEvent.ACTION_MOVE -> {
                if (isRotating && !scaleGestureDetector.isInProgress) {
                    val deltaX = event.x - lastTouchX
                    val deltaY = event.y - lastTouchY
                    
                    // 更新旋转角度
                    currentRotationY += deltaX * rotationSensitivity
                    currentRotationX += deltaY * rotationSensitivity
                    
                    // 限制 X 轴旋转范围
                    currentRotationX = currentRotationX.coerceIn(-PI.toFloat() / 2, PI.toFloat() / 2)
                    
                    // 更新渲染器
                    renderer.rotationX = currentRotationX
                    renderer.rotationY = currentRotationY
                    onTransformChanged(currentRotationX, currentRotationY, currentScale)
                    
                    lastTouchX = event.x
                    lastTouchY = event.y
                }
            }
            
            MotionEvent.ACTION_UP, MotionEvent.ACTION_POINTER_UP -> {
                isRotating = false
            }
        }
        
        return true
    }
    
    /**
     * 重置视角到默认状态
     */
    private fun resetView() {
        currentRotationX = 0f
        currentRotationY = 0f
        currentScale = 1f
        
        renderer.rotationX = currentRotationX
        renderer.rotationY = currentRotationY
        renderer.scale = currentScale
        
        onTransformChanged(currentRotationX, currentRotationY, currentScale)
    }
    
    /**
     * 设置初始变换状态
     */
    fun setInitialTransform(rotationX: Float, rotationY: Float, scale: Float) {
        currentRotationX = rotationX
        currentRotationY = rotationY
        currentScale = scale.coerceIn(minScale, maxScale)
        
        renderer.rotationX = currentRotationX
        renderer.rotationY = currentRotationY
        renderer.scale = currentScale
    }
    
    /**
     * 获取当前变换状态
     */
    fun getCurrentTransform(): Triple<Float, Float, Float> {
        return Triple(currentRotationX, currentRotationY, currentScale)
    }
}

/**
 * 触摸交互配置
 */
data class TouchConfig(
    val enableRotation: Boolean = true,
    val enableZoom: Boolean = true,
    val rotationSensitivity: Float = 0.01f,
    val zoomSensitivity: Float = 1.0f,
    val minZoom: Float = 0.5f,
    val maxZoom: Float = 3.0f
)

/**
 * 模型加载状态
 */
sealed class ModelLoadState {
    object Loading : ModelLoadState()
    object Success : ModelLoadState()
    data class Error(val message: String) : ModelLoadState()
}
