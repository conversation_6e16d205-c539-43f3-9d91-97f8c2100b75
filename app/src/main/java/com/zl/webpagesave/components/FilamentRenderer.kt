package com.zl.webpagesave.components

import android.content.Context
import android.view.Choreographer
import android.view.SurfaceView
import com.google.android.filament.*
import com.google.android.filament.android.DisplayHelper
import com.google.android.filament.android.UiHelper
import com.google.android.filament.gltfio.AssetLoader
import com.google.android.filament.gltfio.FilamentAsset
import com.google.android.filament.gltfio.ResourceLoader
import com.google.android.filament.utils.Float3
import java.nio.ByteBuffer
import kotlin.math.*

/**
 * 简化的 Filament 3D 渲染器
 *
 * 封装了 Filament 引擎的基本初始化和渲染逻辑
 */
class FilamentRenderer(
    val context: Context,
    private val surfaceView: SurfaceView
) : Choreographer.FrameCallback {

    // Filament 核心组件
    private lateinit var engine: Engine
    private lateinit var renderer: Renderer
    private lateinit var scene: Scene
    private lateinit var view: View
    private lateinit var camera: Camera
    private lateinit var swapChain: SwapChain

    // 辅助组件
    private lateinit var uiHelper: UiHelper
    private lateinit var displayHelper: DisplayHelper
    private lateinit var assetLoader: AssetLoader
    private lateinit var resourceLoader: ResourceLoader

    // 模型
    private var filamentAsset: FilamentAsset? = null

    // 渲染状态
    private var isInitialized = false
    private var isDestroyed = false
    
    // 触摸交互状态
    var rotationX: Float = 0f
        set(value) {
            field = value
            updateCameraTransform()
        }
    
    var rotationY: Float = 0f
        set(value) {
            field = value
            updateCameraTransform()
        }
    
    var scale: Float = 1f
        set(value) {
            field = value.coerceIn(0.5f, 3.0f)
            updateCameraTransform()
        }
    
    // 回调接口
    var onModelLoaded: ((Boolean) -> Unit)? = null
    var onError: ((String) -> Unit)? = null
    
    /**
     * 初始化 Filament 引擎
     */
    fun initialize() {
        try {
            // 初始化 Filament 引擎
            engine = Engine.create()
            renderer = engine.createRenderer()
            scene = engine.createScene()

            // 创建相机实体
            val cameraEntity = EntityManager.get().create()
            camera = engine.createCamera(cameraEntity).apply {
                setProjection(45.0, surfaceView.width.toDouble() / surfaceView.height.toDouble(), 0.1, 20.0, Camera.Fov.VERTICAL)
            }

            // 创建视图
            view = engine.createView().apply {
                this.camera = <EMAIL>
                this.scene = <EMAIL>

                // 设置渲染选项
                renderQuality = renderQuality.apply {
                    hdrColorBuffer = View.QualityLevel.MEDIUM
                }

                // 设置抗锯齿
                antiAliasing = View.AntiAliasing.FXAA
                dithering = View.Dithering.TEMPORAL
            }

            // 初始化 UI 助手
            uiHelper = UiHelper(UiHelper.ContextErrorPolicy.DONT_CHECK).apply {
                renderCallback = SurfaceCallback()
                attachTo(surfaceView)
            }

            // 初始化显示助手
            displayHelper = DisplayHelper(context)

            // 创建资源加载器
            assetLoader = AssetLoader(engine, MaterialProvider(engine), EntityManager.get())
            resourceLoader = ResourceLoader(engine)

            // 设置默认光照
            setupLighting()

            // 设置默认相机位置
            updateCameraTransform()

            isInitialized = true

        } catch (e: Exception) {
            onError?.invoke("Filament 初始化失败: ${e.message}")
        }
    }
    
    /**
     * 加载 glTF 模型
     */
    fun loadModel(modelPath: String) {
        if (!isInitialized) {
            onError?.invoke("Filament 引擎未初始化")
            return
        }

        try {
            // 从 assets 读取模型文件
            val buffer = context.assets.open(modelPath).use { inputStream ->
                ByteBuffer.allocate(inputStream.available()).apply {
                    inputStream.read(array())
                    rewind()
                }
            }

            // 加载模型
            filamentAsset = assetLoader.createAssetFromBinary(buffer)
            filamentAsset?.let { asset ->
                // 加载资源
                resourceLoader.loadResources(asset)

                // 添加到场景
                scene.addEntities(asset.entities)

                // 调整模型位置和大小
                adjustModelTransform(asset)

                onModelLoaded?.invoke(true)
            } ?: run {
                onError?.invoke("模型加载失败")
            }

        } catch (e: Exception) {
            onError?.invoke("模型加载错误: ${e.message}")
        }
    }
    
    /**
     * 设置场景光照
     */
    private fun setupLighting() {
        // 创建定向光
        val sunlight = EntityManager.get().create()
        LightManager.Builder(LightManager.Type.DIRECTIONAL)
            .color(1.0f, 1.0f, 1.0f)
            .intensity(100000.0f)
            .direction(0.0f, -1.0f, -1.0f)
            .castShadows(true)
            .build(engine, sunlight)
        scene.addEntity(sunlight)

        // 创建简单的环境光（不使用IBL）
        scene.indirectLight = IndirectLight.Builder()
            .intensity(30000.0f)
            .build(engine)
    }
    
    /**
     * 调整模型变换
     */
    private fun adjustModelTransform(asset: FilamentAsset) {
        val tm = engine.transformManager
        val root = asset.root

        // 获取模型边界框
        val aabb = asset.boundingBox
        val center = Float3(
            (aabb.center.x),
            (aabb.center.y),
            (aabb.center.z)
        )

        // 计算缩放比例
        val size = maxOf(
            aabb.halfExtent.x * 2,
            aabb.halfExtent.y * 2,
            aabb.halfExtent.z * 2
        )
        val modelScale = 2.0f / size

        // 设置变换矩阵
        val transform = FloatArray(16)
        android.opengl.Matrix.setIdentityM(transform, 0)
        android.opengl.Matrix.scaleM(transform, 0, modelScale, modelScale, modelScale)
        android.opengl.Matrix.translateM(transform, 0, -center.x, -center.y, -center.z)

        tm.setTransform(tm.getInstance(root), transform)
    }
    
    /**
     * 更新相机变换
     */
    private fun updateCameraTransform() {
        if (!isInitialized) return
        
        val distance = 5.0f / scale
        val x = distance * sin(rotationY) * cos(rotationX)
        val y = distance * sin(rotationX)
        val z = distance * cos(rotationY) * cos(rotationX)
        
        camera.lookAt(
            x.toDouble(), y.toDouble(), z.toDouble(),  // eye
            0.0, 0.0, 0.0,                            // center
            0.0, 1.0, 0.0                             // up
        )
    }
    
    /**
     * 开始渲染循环
     */
    fun startRenderLoop() {
        if (isInitialized && !isDestroyed) {
            Choreographer.getInstance().postFrameCallback(this)
        }
    }
    
    /**
     * 停止渲染循环
     */
    fun stopRenderLoop() {
        Choreographer.getInstance().removeFrameCallback(this)
    }
    
    /**
     * Choreographer 帧回调
     */
    override fun doFrame(frameTimeNanos: Long) {
        if (isDestroyed) return

        // 渲染帧
        if (uiHelper.isReadyToRender) {
            renderer.render(view)
        }

        // 继续下一帧
        Choreographer.getInstance().postFrameCallback(this)
    }
    
    /**
     * 销毁资源
     */
    fun destroy() {
        if (isDestroyed) return
        
        stopRenderLoop()
        
        // 清理资源
        filamentAsset?.let { asset ->
            scene.removeEntities(asset.entities)
            assetLoader.destroyAsset(asset)
        }
        
        // 销毁 Filament 组件
        if (::engine.isInitialized) {
            engine.destroyRenderer(renderer)
            engine.destroyView(view)
            engine.destroyScene(scene)
            engine.destroyCamera(camera)
            engine.destroy()
        }
        
        isDestroyed = true
    }
    
    /**
     * Surface 回调
     */
    private inner class SurfaceCallback : UiHelper.RendererCallback {
        override fun onNativeWindowChanged(surface: android.view.Surface) {
            if (::swapChain.isInitialized) {
                engine.destroySwapChain(swapChain)
            }
            swapChain = engine.createSwapChain(surface)
            displayHelper.attach(renderer, surfaceView.display)
        }
        
        override fun onDetachedFromSurface() {
            displayHelper.detach()
            if (::swapChain.isInitialized) {
                engine.destroySwapChain(swapChain)
            }
        }
        
        override fun onResized(width: Int, height: Int) {
            view.viewport = Viewport(0, 0, width, height)
            camera.setProjection(45.0, width.toDouble() / height.toDouble(), 0.1, 20.0)
        }
    }
}
