package com.zl.webpagesave.components

import android.content.res.AssetManager
import android.view.Choreographer
import android.view.SurfaceView
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import com.google.android.filament.View
import com.google.android.filament.android.UiHelper
import com.google.android.filament.utils.ModelViewer
import java.nio.ByteBuffer
import java.nio.ByteOrder

/**
 * 基于 ModelViewer 的 Filament 3D 渲染器
 * 
 * 使用 Google Filament 的 ModelViewer 工具类进行 3D 渲染
 */
class FilamentModelRenderer {
    private lateinit var surfaceView: SurfaceView
    private lateinit var lifecycle: Lifecycle
    
    private lateinit var choreographer: Choreographer
    private lateinit var uiHelper: UiHelper
    private lateinit var modelViewer: ModelViewer
    private lateinit var assets: AssetManager
    
    private val frameScheduler = FrameCallback()
    
    // 回调接口
    var onModelLoaded: ((Boolean) -> Unit)? = null
    var onError: ((String) -> Unit)? = null
    
    // 生命周期观察者
    private val lifecycleObserver = object : DefaultLifecycleObserver {
        override fun onResume(owner: LifecycleOwner) {
            choreographer.postFrameCallback(frameScheduler)
        }
        
        override fun onPause(owner: LifecycleOwner) {
            choreographer.removeFrameCallback(frameScheduler)
        }
        
        override fun onDestroy(owner: LifecycleOwner) {
            choreographer.removeFrameCallback(frameScheduler)
            lifecycle.removeObserver(this)
            
            // 清理资源
            if (::modelViewer.isInitialized) {
                modelViewer.destroyModel()
            }
        }
    }
    
    /**
     * 当 SurfaceView 可用时调用
     */
    fun onSurfaceAvailable(surfaceView: SurfaceView, lifecycle: Lifecycle) {
        try {
            this.surfaceView = surfaceView
            this.lifecycle = lifecycle
            this.assets = surfaceView.context.assets
            
            lifecycle.addObserver(lifecycleObserver)
            
            // 初始化 Choreographer
            choreographer = Choreographer.getInstance()
            
            // 初始化 UiHelper
            uiHelper = UiHelper(UiHelper.ContextErrorPolicy.DONT_CHECK).apply {
                // 设置透明背景
                isOpaque = false
            }
            
            // 初始化 ModelViewer
            modelViewer = ModelViewer(surfaceView)
            
            // 设置触摸监听器
            surfaceView.setOnTouchListener { _, event ->
                modelViewer.onTouchEvent(event)
                true
            }
            
            // 配置透明背景
            modelViewer.scene.skybox = null
            modelViewer.view.blendMode = View.BlendMode.TRANSLUCENT
            
            // 设置渲染质量
            modelViewer.view.apply {
                renderQuality = renderQuality.apply {
                    hdrColorBuffer = View.QualityLevel.MEDIUM
                }
                
                // 设置抗锯齿
                antiAliasing = View.AntiAliasing.FXAA
                dithering = View.Dithering.TEMPORAL
            }
            
        } catch (e: Exception) {
            onError?.invoke("Filament 初始化失败: ${e.message}")
        }
    }
    
    /**
     * 加载 glTF 模型
     */
    fun loadModel(modelPath: String) {
        try {
            // 从 assets 读取模型文件
            val buffer = assets.open(modelPath).use { input ->
                val bytes = ByteArray(input.available())
                input.read(bytes)
                ByteBuffer.allocateDirect(bytes.size).apply {
                    order(ByteOrder.nativeOrder())
                    put(bytes)
                    rewind()
                }
            }
            
            // 加载模型
            modelViewer.loadModelGlb(buffer)
            
            // 调整模型到单位立方体
            modelViewer.transformToUnitCube()
            
            onModelLoaded?.invoke(true)
            
        } catch (e: Exception) {
            onError?.invoke("模型加载失败: ${e.message}")
        }
    }
    
    /**
     * 开始渲染循环
     */
    fun startRenderLoop() {
        if (::choreographer.isInitialized) {
            choreographer.postFrameCallback(frameScheduler)
        }
    }
    
    /**
     * 停止渲染循环
     */
    fun stopRenderLoop() {
        if (::choreographer.isInitialized) {
            choreographer.removeFrameCallback(frameScheduler)
        }
    }
    
    /**
     * 设置相机变换
     */
    fun setCameraTransform(rotationX: Float, rotationY: Float, scale: Float) {
        // ModelViewer 内部处理相机变换，这里可以扩展自定义控制
    }
    
    /**
     * 帧回调
     */
    private inner class FrameCallback : Choreographer.FrameCallback {
        override fun doFrame(frameTimeNanos: Long) {
            if (::choreographer.isInitialized && ::modelViewer.isInitialized) {
                choreographer.postFrameCallback(this)
                modelViewer.render(frameTimeNanos)
            }
        }
    }
    
    /**
     * 销毁资源
     */
    fun destroy() {
        stopRenderLoop()
        
        if (::lifecycle.isInitialized) {
            lifecycle.removeObserver(lifecycleObserver)
        }
        
        if (::modelViewer.isInitialized) {
            modelViewer.destroyModel()
        }
    }
}

/**
 * 触摸交互配置
 */
data class FilamentTouchConfig(
    val enableRotation: Boolean = true,
    val enableZoom: Boolean = true,
    val rotationSensitivity: Float = 1.0f,
    val zoomSensitivity: Float = 1.0f
)

/**
 * 模型加载状态
 */
sealed class FilamentModelLoadState {
    object Loading : FilamentModelLoadState()
    object Success : FilamentModelLoadState()
    data class Error(val message: String) : FilamentModelLoadState()
}
