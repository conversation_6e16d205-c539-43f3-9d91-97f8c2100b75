package com.zl.webpagesave.components

import android.content.Context
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.SurfaceView
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.zl.webpagesave.utils.FilamentHelper

/**
 * 支持触摸交互的 Filament 渲染视图
 */
@Composable
fun TouchableFilamentView(
    modifier: Modifier = Modifier,
    modelPath: String = "models/ABeautifulGame/glTF/ABeautifulGame.gltf",
    onModelLoaded: (Boolean) -> Unit = {},
    onError: (String) -> Unit = {}
) {
    val context = LocalContext.current
    var filamentHelper by remember { mutableStateOf<FilamentHelper?>(null) }
    var isLoading by remember { mutableStateOf(true) }
    var loadError by remember { mutableStateOf<String?>(null) }
    
    DisposableEffect(Unit) {
        onDispose {
            filamentHelper?.destroy()
        }
    }
    
    Box(modifier = modifier) {
        if (isLoading) {
            // 加载状态
            Column(
                modifier = Modifier.align(Alignment.Center),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                CircularProgressIndicator()
                Spacer(modifier = Modifier.height(16.dp))
                Text("加载 3D 模型中...")
                Text(
                    text = "国际象棋场景",
                    style = MaterialTheme.typography.bodySmall
                )
            }
        } else if (loadError != null) {
            // 错误状态
            Column(
                modifier = Modifier.align(Alignment.Center),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "❌ 加载失败",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.error
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = loadError!!,
                    style = MaterialTheme.typography.bodySmall
                )
            }
        } else {
            // Filament 渲染视图
            AndroidView(
                factory = { ctx ->
                    createTouchableSurfaceView(ctx) { helper ->
                        filamentHelper = helper
                    }
                },
                modifier = Modifier.fillMaxSize()
            )
        }
        
        // 触摸提示
        if (!isLoading && loadError == null) {
            Card(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(12.dp)
                ) {
                    Text(
                        text = "🎮 触摸控制",
                        style = MaterialTheme.typography.labelMedium
                    )
                    Text(
                        text = "• 拖拽旋转",
                        style = MaterialTheme.typography.bodySmall
                    )
                    Text(
                        text = "• 双指缩放",
                        style = MaterialTheme.typography.bodySmall
                    )
                }
            }
        }
    }
    
    // 初始化和加载模型
    LaunchedEffect(modelPath) {
        try {
            val helper = FilamentHelper(context)
            if (helper.initialize()) {
                // 延迟一下让 UI 显示加载状态
                kotlinx.coroutines.delay(500)
                
                val success = helper.loadGltfModel(modelPath)
                if (success) {
                    isLoading = false
                    onModelLoaded(true)
                } else {
                    loadError = "无法加载模型文件"
                    isLoading = false
                    onError("模型加载失败")
                }
            } else {
                loadError = "Filament 引擎初始化失败"
                isLoading = false
                onError("引擎初始化失败")
            }
        } catch (e: Exception) {
            loadError = e.message ?: "未知错误"
            isLoading = false
            onError(e.message ?: "未知错误")
        }
    }
}

/**
 * 创建支持触摸交互的 SurfaceView
 */
private fun createTouchableSurfaceView(
    context: Context,
    onHelperCreated: (FilamentHelper) -> Unit
): SurfaceView {
    val surfaceView = SurfaceView(context)
    val filamentHelper = FilamentHelper(context)
    
    // 初始化 Filament
    if (filamentHelper.initialize()) {
        filamentHelper.setupSurface(surfaceView)
        onHelperCreated(filamentHelper)
        
        // 设置触摸交互
        setupTouchInteraction(surfaceView, filamentHelper)
        
        // 开始渲染
        filamentHelper.startRendering()
    }
    
    return surfaceView
}

/**
 * 设置触摸交互
 */
private fun setupTouchInteraction(surfaceView: SurfaceView, filamentHelper: FilamentHelper) {
    var lastX = 0f
    var lastY = 0f
    var isRotating = false
    
    // 缩放手势检测器
    val scaleGestureDetector = ScaleGestureDetector(surfaceView.context,
        object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
            override fun onScale(detector: ScaleGestureDetector): Boolean {
                val scaleFactor = detector.scaleFactor
                filamentHelper.onZoom(1f / scaleFactor) // 反向缩放以符合直觉
                return true
            }
        }
    )
    
    // 普通手势检测器
    val gestureDetector = GestureDetector(surfaceView.context,
        object : GestureDetector.SimpleOnGestureListener() {
            override fun onDoubleTap(e: MotionEvent): Boolean {
                // 双击重置相机
                filamentHelper.resetCamera()
                return true
            }
        }
    )
    
    surfaceView.setOnTouchListener { _, event ->
        // 先处理缩放手势
        scaleGestureDetector.onTouchEvent(event)
        gestureDetector.onTouchEvent(event)
        
        // 如果正在缩放，不处理旋转
        if (scaleGestureDetector.isInProgress) {
            return@setOnTouchListener true
        }
        
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                lastX = event.x
                lastY = event.y
                isRotating = true
                true
            }
            MotionEvent.ACTION_MOVE -> {
                if (isRotating) {
                    val deltaX = event.x - lastX
                    val deltaY = event.y - lastY
                    
                    // 旋转相机
                    filamentHelper.onRotate(deltaX, deltaY)
                    
                    lastX = event.x
                    lastY = event.y
                }
                true
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                isRotating = false
                true
            }
            else -> false
        }
    }
}

/**
 * 模型加载状态
 */
sealed class ModelLoadState {
    object Loading : ModelLoadState()
    object Success : ModelLoadState()
    data class Error(val message: String) : ModelLoadState()
}

/**
 * 触摸交互配置
 */
data class TouchConfig(
    val enableRotation: Boolean = true,
    val enableZoom: Boolean = true,
    val enableDoubleTapReset: Boolean = true,
    val rotationSensitivity: Float = 0.01f,
    val zoomSensitivity: Float = 1.0f,
    val minZoom: Double = 1.0,
    val maxZoom: Double = 50.0
)
