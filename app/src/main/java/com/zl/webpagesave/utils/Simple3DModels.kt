package com.zl.webpagesave.utils

import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import kotlin.math.*

/**
 * 简单的 3D 模型生成器
 * 
 * 这个类提供了一些基础的 3D 几何体生成方法，
 * 用于在 Canvas 上绘制简单的 3D 效果。
 */
object Simple3DModels {
    
    /**
     * 绘制旋转的立方体
     */
    fun DrawScope.drawRotatingCube(
        centerX: Float,
        centerY: Float,
        size: Float,
        rotationX: Float = 0f,
        rotationY: Float = 0f,
        rotationZ: Float = 0f
    ) {
        val halfSize = size / 2
        
        // 立方体的8个顶点
        val vertices = arrayOf(
            Triple(-halfSize, -halfSize, -halfSize), // 0: 左下后
            Triple(halfSize, -halfSize, -halfSize),  // 1: 右下后
            Triple(halfSize, halfSize, -halfSize),   // 2: 右上后
            Triple(-halfSize, halfSize, -halfSize),  // 3: 左上后
            Triple(-halfSize, -halfSize, halfSize),  // 4: 左下前
            Triple(halfSize, -halfSize, halfSize),   // 5: 右下前
            Triple(halfSize, halfSize, halfSize),    // 6: 右上前
            Triple(-halfSize, halfSize, halfSize)    // 7: 左上前
        )
        
        // 应用旋转变换
        val rotatedVertices = vertices.map { (x, y, z) ->
            val rotated = rotatePoint(x, y, z, rotationX, rotationY, rotationZ)
            projectTo2D(rotated.first, rotated.second, rotated.third, centerX, centerY)
        }
        
        // 定义立方体的面（顶点索引）
        val faces = arrayOf(
            intArrayOf(0, 1, 2, 3), // 后面
            intArrayOf(4, 7, 6, 5), // 前面
            intArrayOf(0, 4, 5, 1), // 下面
            intArrayOf(2, 6, 7, 3), // 上面
            intArrayOf(0, 3, 7, 4), // 左面
            intArrayOf(1, 5, 6, 2)  // 右面
        )
        
        val faceColors = arrayOf(
            Color(0xFF1976D2), // 后面 - 深蓝
            Color(0xFF2196F3), // 前面 - 蓝色
            Color(0xFF42A5F5), // 下面 - 浅蓝
            Color(0xFF64B5F6), // 上面 - 更浅蓝
            Color(0xFF90CAF9), // 左面 - 很浅蓝
            Color(0xFFBBDEFB)  // 右面 - 最浅蓝
        )
        
        // 绘制面（从后往前）
        faces.forEachIndexed { index, face ->
            val path = Path().apply {
                val firstVertex = rotatedVertices[face[0]]
                moveTo(firstVertex.x, firstVertex.y)
                for (i in 1 until face.size) {
                    val vertex = rotatedVertices[face[i]]
                    lineTo(vertex.x, vertex.y)
                }
                close()
            }
            
            // 填充面
            drawPath(
                path = path,
                color = faceColors[index],
                alpha = 0.7f
            )
            
            // 绘制边框
            drawPath(
                path = path,
                color = Color.Black,
                style = Stroke(width = 2f)
            )
        }
    }
    
    /**
     * 绘制旋转的金字塔
     */
    fun DrawScope.drawRotatingPyramid(
        centerX: Float,
        centerY: Float,
        size: Float,
        rotation: Float = 0f
    ) {
        val halfSize = size / 2
        val height = size * 0.8f
        
        // 金字塔的5个顶点
        val vertices = arrayOf(
            Triple(-halfSize, 0f, -halfSize), // 0: 左后底
            Triple(halfSize, 0f, -halfSize),  // 1: 右后底
            Triple(halfSize, 0f, halfSize),   // 2: 右前底
            Triple(-halfSize, 0f, halfSize),  // 3: 左前底
            Triple(0f, -height, 0f)           // 4: 顶点
        )
        
        // 应用旋转变换
        val rotatedVertices = vertices.map { (x, y, z) ->
            val rotated = rotatePoint(x, y, z, 0f, rotation, 0f)
            projectTo2D(rotated.first, rotated.second, rotated.third, centerX, centerY)
        }
        
        // 定义金字塔的面
        val faces = arrayOf(
            intArrayOf(0, 1, 2, 3), // 底面
            intArrayOf(0, 1, 4),    // 后面
            intArrayOf(1, 2, 4),    // 右面
            intArrayOf(2, 3, 4),    // 前面
            intArrayOf(3, 0, 4)     // 左面
        )
        
        val faceColors = arrayOf(
            Color(0xFF4CAF50), // 底面 - 绿色
            Color(0xFF66BB6A), // 后面
            Color(0xFF81C784), // 右面
            Color(0xFF9CCC65), // 前面
            Color(0xFFAED581)  // 左面
        )
        
        // 绘制面
        faces.forEachIndexed { index, face ->
            val path = Path().apply {
                val firstVertex = rotatedVertices[face[0]]
                moveTo(firstVertex.x, firstVertex.y)
                for (i in 1 until face.size) {
                    val vertex = rotatedVertices[face[i]]
                    lineTo(vertex.x, vertex.y)
                }
                close()
            }
            
            drawPath(
                path = path,
                color = faceColors[index],
                alpha = 0.8f
            )
            
            drawPath(
                path = path,
                color = Color.Black,
                style = Stroke(width = 1.5f)
            )
        }
    }
    
    /**
     * 绘制旋转的八面体
     */
    fun DrawScope.drawRotatingOctahedron(
        centerX: Float,
        centerY: Float,
        size: Float,
        rotation: Float = 0f
    ) {
        val halfSize = size / 2
        
        // 八面体的6个顶点
        val vertices = arrayOf(
            Triple(0f, -halfSize, 0f),  // 0: 上顶点
            Triple(0f, halfSize, 0f),   // 1: 下顶点
            Triple(-halfSize, 0f, 0f),  // 2: 左顶点
            Triple(halfSize, 0f, 0f),   // 3: 右顶点
            Triple(0f, 0f, -halfSize),  // 4: 后顶点
            Triple(0f, 0f, halfSize)    // 5: 前顶点
        )
        
        // 应用旋转变换
        val rotatedVertices = vertices.map { (x, y, z) ->
            val rotated = rotatePoint(x, y, z, rotation, rotation * 0.7f, rotation * 0.5f)
            projectTo2D(rotated.first, rotated.second, rotated.third, centerX, centerY)
        }
        
        // 定义八面体的面
        val faces = arrayOf(
            intArrayOf(0, 2, 4), // 上左后
            intArrayOf(0, 4, 3), // 上右后
            intArrayOf(0, 3, 5), // 上右前
            intArrayOf(0, 5, 2), // 上左前
            intArrayOf(1, 4, 2), // 下左后
            intArrayOf(1, 3, 4), // 下右后
            intArrayOf(1, 5, 3), // 下右前
            intArrayOf(1, 2, 5)  // 下左前
        )
        
        val faceColors = arrayOf(
            Color(0xFFFF9800), Color(0xFFFFB74D),
            Color(0xFFFFCC02), Color(0xFFFFF176),
            Color(0xFFFF5722), Color(0xFFFF8A65),
            Color(0xFFFF6F00), Color(0xFFFFAB40)
        )
        
        // 绘制面
        faces.forEachIndexed { index, face ->
            val path = Path().apply {
                val firstVertex = rotatedVertices[face[0]]
                moveTo(firstVertex.x, firstVertex.y)
                for (i in 1 until face.size) {
                    val vertex = rotatedVertices[face[i]]
                    lineTo(vertex.x, vertex.y)
                }
                close()
            }
            
            drawPath(
                path = path,
                color = faceColors[index],
                alpha = 0.8f
            )
            
            drawPath(
                path = path,
                color = Color.Black,
                style = Stroke(width = 1f)
            )
        }
    }
    
    /**
     * 旋转点的坐标
     */
    private fun rotatePoint(x: Float, y: Float, z: Float, rotX: Float, rotY: Float, rotZ: Float): Triple<Float, Float, Float> {
        val radX = Math.toRadians(rotX.toDouble())
        val radY = Math.toRadians(rotY.toDouble())
        val radZ = Math.toRadians(rotZ.toDouble())
        
        val cosX = cos(radX).toFloat()
        val sinX = sin(radX).toFloat()
        val cosY = cos(radY).toFloat()
        val sinY = sin(radY).toFloat()
        val cosZ = cos(radZ).toFloat()
        val sinZ = sin(radZ).toFloat()
        
        // 绕X轴旋转
        var newY = y * cosX - z * sinX
        var newZ = y * sinX + z * cosX
        var newX = x
        
        // 绕Y轴旋转
        val tempX = newX * cosY + newZ * sinY
        newZ = -newX * sinY + newZ * cosY
        newX = tempX
        
        // 绕Z轴旋转
        val finalX = newX * cosZ - newY * sinZ
        val finalY = newX * sinZ + newY * cosZ
        
        return Triple(finalX, finalY, newZ)
    }
    
    /**
     * 将3D坐标投影到2D屏幕坐标
     */
    private fun projectTo2D(x: Float, y: Float, z: Float, centerX: Float, centerY: Float): Offset {
        val distance = 400f // 观察距离
        val scale = distance / (distance + z)
        return Offset(
            centerX + x * scale,
            centerY + y * scale
        )
    }
}
