# Filament 3D 渲染引擎集成指南

本项目演示了如何在 Android 应用中集成 Google Filament 3D 渲染引擎。

## 什么是 Filament？

Filament 是 Google 开发的基于物理的实时渲染引擎，专为移动设备优化。它提供了：

- 基于物理的渲染 (PBR)
- 实时阴影和光照
- 高性能的移动端优化
- 跨平台支持 (Android, iOS, Web, Desktop)
- glTF 2.0 模型支持

## 项目结构

```
app/src/main/java/com/zl/webpagesave/
├── screens/
│   └── FilamentDemoScreen.kt          # Filament 演示页面
├── utils/
│   └── FilamentHelper.kt              # Filament 辅助工具类
└── MainActivity.kt
```

## 依赖配置

### 1. 在 `gradle/libs.versions.toml` 中添加版本定义

```toml
[versions]
filament = "1.51.5"

[libraries]
filament-android = { group = "com.google.android.filament", name = "filament-android", version.ref = "filament" }
filament-utils-android = { group = "com.google.android.filament", name = "filament-utils-android", version.ref = "filament" }
gltfio-android = { group = "com.google.android.filament", name = "gltfio-android", version.ref = "filament" }

[bundles]
filament = [
    "filament-android",
    "filament-utils-android",
    "gltfio-android"
]
```

### 2. 在 `app/build.gradle.kts` 中添加依赖

```kotlin
dependencies {
    // Filament 3D渲染依赖
    implementation(libs.bundles.filament)
}

android {
    // 支持 Filament 的 native 库
    packagingOptions {
        pickFirst "**/libc++_shared.so"
        pickFirst "**/libfilament-jni.so"
    }
}
```

## 基本使用

### 1. 创建 Filament 引擎

```kotlin
// 初始化引擎
val engine = Engine.create()
val renderer = engine.createRenderer()
val scene = engine.createScene()

// 创建相机
val cameraEntity = engine.entityManager.create()
val camera = engine.createCamera(cameraEntity)

// 创建视图
val view = engine.createView()
view.camera = camera
view.scene = scene
```

### 2. 设置渲染表面

```kotlin
// 使用 SurfaceView
val surfaceView = SurfaceView(context)

// 设置 UI Helper
val uiHelper = UiHelper(UiHelper.ContextErrorPolicy.DONT_CHECK)
uiHelper.renderCallback = object : UiHelper.RendererCallback {
    override fun onNativeWindowChanged(surface: Surface) {
        displayHelper.attach(renderer, surface)
    }
    
    override fun onDetachedFromSurface() {
        displayHelper.detach()
    }
    
    override fun onResized(width: Int, height: Int) {
        view.viewport = Viewport(0, 0, width, height)
    }
}
uiHelper.attachTo(surfaceView)
```

### 3. 渲染循环

```kotlin
val choreographer = Choreographer.getInstance()
val frameCallback = object : Choreographer.FrameCallback {
    override fun doFrame(frameTimeNanos: Long) {
        choreographer.postFrameCallback(this)
        
        if (renderer.beginFrame(swapChain, frameTimeNanos)) {
            renderer.render(view)
            renderer.endFrame()
        }
    }
}
choreographer.postFrameCallback(frameCallback)
```

## 在 Compose 中使用

```kotlin
@Composable
fun FilamentRenderView() {
    AndroidView(
        factory = { context ->
            SurfaceView(context).apply {
                // 初始化 Filament
                val filamentHelper = FilamentHelper(context)
                filamentHelper.initialize()
                filamentHelper.setupSurface(this)
                filamentHelper.startRendering()
            }
        },
        modifier = Modifier.fillMaxSize()
    )
}
```

## 功能特性

### 1. 基于物理的渲染 (PBR)
- 真实的材质表现
- 准确的光照计算
- 环境反射

### 2. 高性能优化
- 移动端 GPU 优化
- 高效的渲染管线
- 自动 LOD 系统

### 3. 材质系统
- 标准 PBR 材质
- 自定义着色器
- 纹理支持

### 4. 光照系统
- 方向光、点光源、聚光灯
- 实时阴影
- IBL (基于图像的光照)

## 最佳实践

### 1. 资源管理
```kotlin
// 始终在适当的时候清理资源
override fun onDestroy() {
    super.onDestroy()
    filamentHelper.destroy()
}
```

### 2. 性能优化
- 使用合适的纹理分辨率
- 控制多边形数量
- 合理使用光源数量
- 在低端设备上降低渲染质量

### 3. 错误处理
```kotlin
try {
    val engine = Engine.create()
    // ... 其他初始化代码
} catch (e: Exception) {
    Log.e("Filament", "Failed to initialize Filament", e)
    // 提供降级方案
}
```

## 常见问题

### Q: 如何加载 3D 模型？
A: 使用 glTF 格式和 gltfio 库：
```kotlin
val assetLoader = AssetLoader(engine, MaterialProvider(engine), EntityManager.get())
val asset = assetLoader.createAssetFromBinary(gltfData)
scene.addEntities(asset.entities)
```

### Q: 如何处理触摸交互？
A: 实现触摸监听器来控制相机：
```kotlin
surfaceView.setOnTouchListener { _, event ->
    // 处理触摸事件，更新相机位置
    true
}
```

### Q: 如何优化性能？
A: 
- 使用合适的渲染质量设置
- 控制场景复杂度
- 使用纹理压缩
- 实现视锥体剔除

## 参考资源

- [Filament 官方文档](https://google.github.io/filament/)
- [Filament GitHub 仓库](https://github.com/google/filament)
- [Filament 示例项目](https://github.com/google/filament/tree/main/android/samples)
- [Material Design 3D 指南](https://material.io/design/environment/3d-principles.html)

## 许可证

Filament 使用 Apache 2.0 许可证。详情请参考 [LICENSE](https://github.com/google/filament/blob/main/LICENSE) 文件。
