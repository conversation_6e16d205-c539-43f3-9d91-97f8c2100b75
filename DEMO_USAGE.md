# Filament Demo 使用指南

## 快速开始

### 1. 构建和安装应用
```bash
# 构建项目
./gradlew build

# 安装到设备
./gradlew installDebug
```

### 2. 访问 Filament Demo

#### 步骤 1: 打开应用
- 在设备上找到 "WebPageSave" 应用
- 点击打开应用

#### 步骤 2: 进入 Filament Demo
- 在首页找到分类导航区域
- 点击 "3D演示" 图标（ViewInAr 图标）
- 应用会导航到 Filament Demo 页面

#### 步骤 3: 体验功能
- 查看 3D 渲染区域（当前显示占位符）
- 点击 "开始渲染" 按钮
- 尝试 "重置场景" 和 "重置相机" 功能
- 阅读功能特性和集成说明

## 页面功能详解

### 🎮 3D 渲染区域
```
┌─────────────────────────────────┐
│        ● ● ●                    │
│                                 │
│      🎮 立方体演示               │
│    (自动旋转的3D模型)            │
│                                 │
│   旋转角度: 45°                  │
│   自动切换模型中...              │
└─────────────────────────────────┘
```
- ✅ **真实的 3D 模型展示**：立方体、金字塔、八面体
- ✅ **自动旋转动画**：实时角度显示
- ✅ **自动模型切换**：每3秒切换一种几何体
- ✅ **多轴旋转效果**：X、Y、Z轴同时旋转

### 🎛️ 控制面板
```
┌─────────────────────────────────┐
│  Filament 3D 渲染引擎演示        │
│                                 │
│  这是一个使用 Google Filament    │
│  渲染引擎的简单演示...           │
│                                 │
│  [开始渲染]  [停止渲染]          │
│                                 │
│      [重置相机]                 │
└─────────────────────────────────┘
```

#### 按钮功能：
- **开始渲染/停止渲染**: 切换渲染状态
- **重置场景**: 重置 3D 场景到初始状态
- **重置相机**: 重置相机位置和角度

### 📋 功能特性介绍
```
┌─────────────────────────────────┐
│  功能特性                       │
│                                 │
│  • 基于物理的渲染 (PBR)         │
│  • 实时阴影和光照               │
│  • 材质系统和纹理               │
│  • 后处理效果                   │
│  • 移动设备优化                 │
│  • 跨平台支持                   │
│  • glTF 2.0 模型支持            │
│  • HDR 环境贴图                 │
└─────────────────────────────────┘
```

### 📖 集成说明
```
┌─────────────────────────────────┐
│  集成说明                       │
│                                 │
│  要在您的项目中使用 Filament，   │
│  请按以下步骤操作：              │
│                                 │
│  1. 添加 Filament 依赖到        │
│     build.gradle                │
│  2. 创建 Engine 和 Renderer     │
│     实例                        │
│  3. 设置 Scene、View 和 Camera  │
│  4. 加载 3D 模型或创建几何体     │
│  5. 配置材质和光照               │
│  6. 实现渲染循环                │
│                                 │
│  💡 提示：这是一个演示界面...    │
└─────────────────────────────────┘
```

## 代码结构说明

### 主要文件
1. **FilamentDemoScreen.kt** - 演示页面 UI
2. **FilamentHelper.kt** - Filament 集成工具类
3. **MainScreen.kt** - 主页面导航
4. **HomeScreen.kt** - 首页（包含 3D演示 入口）

### 导航流程
```
首页 (HomeScreen)
    ↓ 点击 "3D演示"
Filament Demo (FilamentDemoScreen)
    ↓ 点击返回按钮
首页 (HomeScreen)
```

## 开发说明

### 当前实现状态
- ✅ UI 界面完成
- ✅ 导航功能完成
- ✅ 依赖配置完成
- ✅ 基础框架完成
- ✅ **3D 模型展示完成** - 包含立方体、金字塔、八面体
- ✅ **动画效果完成** - 自动旋转和模型切换
- ⚠️ 真实 Filament 渲染需要完整实现

### 下一步开发
1. **实现真实的 Filament 渲染**
   ```kotlin
   // 需要完善 FilamentHelper.kt 中的实际 API 调用
   val engine = Engine.create()
   val renderer = engine.createRenderer()
   // ... 更多 Filament API 调用
   ```

2. **添加 3D 模型加载**
   ```kotlin
   // 加载 glTF 模型
   val assetLoader = AssetLoader(engine, materialProvider, entityManager)
   val asset = assetLoader.createAssetFromBinary(gltfData)
   ```

3. **实现触摸交互**
   ```kotlin
   // 添加触摸手势处理
   surfaceView.setOnTouchListener { _, event ->
       // 处理相机控制
   }
   ```

### 测试建议
1. **功能测试**
   - 测试页面导航
   - 测试按钮交互
   - 测试状态切换

2. **UI 测试**
   - 测试不同屏幕尺寸
   - 测试横竖屏切换
   - 测试主题适配

3. **性能测试**
   - 测试内存使用
   - 测试渲染性能
   - 测试电池消耗

## 故障排除

### 常见问题
1. **构建失败**
   - 检查 Filament 依赖版本
   - 确认 NDK 配置正确
   - 清理并重新构建

2. **应用崩溃**
   - 检查设备 OpenGL ES 支持
   - 查看 Logcat 错误信息
   - 确认权限配置

3. **渲染问题**
   - 检查 Surface 创建
   - 确认 Filament 引擎初始化
   - 验证着色器编译

### 调试技巧
```bash
# 查看应用日志
adb logcat | grep WebPageSave

# 检查 GPU 信息
adb shell dumpsys SurfaceFlinger

# 监控性能
adb shell top | grep com.zl.webpagesave
```

## 总结

这个 Filament Demo 提供了：
- 🎯 完整的演示界面
- 🔧 基础的集成框架  
- 📚 详细的使用文档
- 🚀 扩展开发指南

通过这个 demo，您可以：
- 了解 Filament 的基本概念
- 学习 Android 3D 应用开发
- 获得实际的代码参考
- 快速开始自己的项目
